/**
 * 阿里云ICE模板相关API
 * 通过后端代理调用阿里云ICE SDK
 */
import config from '@/config'
import { getToken } from '@/utils/auth'
import request from '@/utils/request'

// ============================================================================
// 类型定义 - 根据阿里云ICE官方文档
// ============================================================================

export interface CreateTemplateRequest {
    Name: string        // @JsonProperty("Name")
    Type?: string       // @JsonProperty("Type")
    Config: string      // @JsonProperty("Config") - 模板配置JSON字符串
    CoverUrl?: string   // @JsonProperty("CoverUrl") - 封面URL
    PreviewMedia?: string // @JsonProperty("PreviewMedia")
    Status?: string     // @JsonProperty("Status")
    Source?: string     // @JsonProperty("Source")
    RelatedMediaids?: string // @JsonProperty("RelatedMediaids")
}

export interface UpdateTemplateRequest {
    TemplateId: string    // @JsonProperty("TemplateId")
    Name?: string         // @JsonProperty("Name")
    Config: string        // @JsonProperty("Config") - 模板配置JSON字符串
    CoverUrl?: string     // @JsonProperty("CoverUrl")
    PreviewMedia?: string // @JsonProperty("PreviewMedia")
    Status?: string       // @JsonProperty("Status")
    Source?: string       // @JsonProperty("Source")
    RelatedMediaids?: string // @JsonProperty("RelatedMediaids")
}

export interface TemplateInfo {
    TemplateId: string
    Name: string
    Type: string
    Config: string // Timeline配置
    PreviewMedia?: string
    Status: string
    CreateSource?: string
    ModifiedSource?: string
    PreviewMediaStatus?: string
    CreationTime?: string
    ModifiedTime?: string
    CoverURL?: string
    ClipsParam?: any
    RelatedMediaids?: {
        video?: string[]
        audio?: string[]
        image?: string[]
    }
}

export interface TemplateListParams {
    pageNo?: number
    pageSize?: number
    type?: string
    status?: string
    createSource?: string
    keyword?: string
    sortType?: string
}

export interface TemplateListResponse {
    Templates: TemplateInfo[]
    TotalCount: number
}

export interface ApiResponse<T> {
    code: number
    msg: string
    data: T
}

// ============================================================================
// 兼容性类型（保持向后兼容）
// ============================================================================

export interface AddTemplateRequest extends CreateTemplateRequest { }
export interface AddTemplateResponse extends TemplateInfo { }
export interface ListTemplatesRequest extends TemplateListParams { }
export interface ListTemplatesResponse extends TemplateListResponse { }
export interface DeleteTemplateRequest {
    templateId: string
}
export interface DeleteTemplateResponse {
    success: boolean
}
export interface UpdateTemplateResponse {
    success: boolean
}

// ============================================================================
// API接口
// ============================================================================

/**
 * 创建模板
 */
export function addTemplate(data: CreateTemplateRequest): Promise<ApiResponse<TemplateInfo>> {
    console.log('📤 API发送模板创建请求:', data)
    // 直接传递数据，字段名已经匹配后端 @JsonProperty 注解
    const requestData = {
        Name: data.Name,
        Type: data.Type || 'Timeline',
        Config: data.Config,
        CoverUrl: data.CoverUrl,
        PreviewMedia: data.PreviewMedia,
        Status: data.Status,
        Source: data.Source || 'WebSDK',
        RelatedMediaids: data.RelatedMediaids
    }

    return request({
        url: '/video/template/add',
        method: 'POST',
        data: requestData
    })
}

/**
 * 获取模板详情
 */
export function getTemplate(templateId: string, params?: { relatedMediaidFlag?: string }): Promise<ApiResponse<{ RequestId: string; Template: TemplateInfo }>> {
    return request({
        url: `/video/template/${templateId}`,
        method: 'GET',
        params: {
            relatedMediaidFlag: params?.relatedMediaidFlag || '0'
        }
    })
}

/**
 * 获取模板列表
 */
export function listTemplates(params: TemplateListParams = {}): Promise<ApiResponse<TemplateListResponse>> {
    return request({
        url: '/video/template/list',
        method: 'GET',
        params: {
            pageNo: params.pageNo,
            pageSize: params.pageSize,
            type: params.type,
            status: params.status,
            createSource: params.createSource,
            keyword: params.keyword,
            sortType: params.sortType || 'CreationTime:Desc'
        }
    })
}

/**
 * 更新模板
 */
export function updateTemplate(data: UpdateTemplateRequest): Promise<ApiResponse<UpdateTemplateResponse>> {
    // 直接传递数据，字段名已经匹配后端 @JsonProperty 注解
    const requestData = {
        TemplateId: data.TemplateId,
        Name: data.Name,
        Config: data.Config,
        CoverUrl: data.CoverUrl,
        PreviewMedia: data.PreviewMedia,
        Status: data.Status,
        Source: data.Source,
        RelatedMediaids: data.RelatedMediaids
    }

    return request({
        url: '/video/template/update',
        method: 'PUT',
        data: requestData
    })
}


/**
 * 删除模板
 */
export function deleteTemplate(templateIds: string[]): Promise<ApiResponse<DeleteTemplateResponse>> {
    return request({
        url: '/video/template/delete',
        method: 'POST',
        data: {
            templateIds: templateIds.join(',')
        }
    })
}

/**
 * 获取模板素材地址
 */
export function getTemplateMaterials(templateId: string, fileList?: string): Promise<ApiResponse<any>> {
    return request({
        url: `/video/template/${templateId}/materials`,
        method: 'GET',
        params: {
            fileList
        }
    })
}

// ============================================================================
// 兼容性接口（保持向后兼容）
// ============================================================================

/**
 * 获取模板列表（兼容旧接口）
 */
export function getTemplateList(params: TemplateListParams): Promise<ApiResponse<TemplateListResponse>> {
    return listTemplates(params)
}

/** 根据模版编号合成视频 */

export interface SynthesizeVideoResponse {
    fileId: string
    fileName: string
    url: string
    duration: number
}
export interface SynthesizeVideoRequest {
    templateId: string
    clipsParam: string
    files: Array<File>
}
export interface File {
    name: string
    uri: string
}

export function synthesizeVideo(data: SynthesizeVideoRequest): Promise<ApiResponse<SynthesizeVideoResponse>> {
    console.log("synthesizeVideo", data)
    return new Promise((resolve, reject) => {
        uni.uploadFile({
            url: `${config.baseUrl}/media/mediaProducing/submit`,
            files: data.files,
            formData: {
                templateId: data.templateId,
                clipsParam: data.clipsParam,
            },
            header: {
                'Authorization': `Bearer ${getToken()}`
            },
            success: (res) => {
                console.log("synthesizeVideo", res)
                if (res.statusCode == 200) {
                    try {
                        const result = JSON.parse(res.data)
                        resolve(result)
                    } catch (error) {
                        reject({
                            code: -1,
                            msg: "解析服务器响应失败",
                            originalResponse: res,
                            error: error
                        })
                    }
                } else {
                    reject({
                        code: res.statusCode,
                        msg: "请求失败",
                        originalResponse: res
                    })
                }
            },
            fail: (err) => {
                console.error("视频合成上传失败>>>", err)
                reject({
                    code: -1,
                    msg: "网络请求失败",
                    error: err
                })
            },
            complete: () => {
                console.log("视频合成请求已完成")
            }
        })
    })

}

