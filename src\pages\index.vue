<template>
  <view class="page">
    <up-navbar
      @leftClick="goBack"
      title="视频剪辑"
      :safeAreaInsetTop="true"
      bgColor="#fff"
      leftIconColor="#000"
      titleStyle="color: #000;"
    />
    <!-- 主要内容区域 -->
    <scroll-view class="container" scroll-y="true">
      <view class="section">
        <TemplateSelector v-model="template" placeholder="点击选择模版" />
      </view>

      <view class="section upload-section">
        <view class="section-title">上传视频</view>
        <view class="upload-container">
          <up-upload
            :fileList="videoList"
            @afterRead="afterRead"
            @delete="deletePic"
            multiple
            :maxCount="getMaxMediaCount()"
            accept="video"
            uploadIcon="camera-fill"
            uploadIconSize="26"
            width="100"
            height="100"
            previewSize="100"
            :disabled = "isUploadDisabled"
          >
            <template #default>
              <view class="custom-upload-btn">
                <view class="upload-icon">
                  <text class="iconfont icon-video"></text>
                </view>
                <text class="upload-text">{{ getUploadText() }}</text>
              </view>
            </template>
          </up-upload>
        </view>
        <view class="upload-tips" v-if="videoList.length > 0">
          <text
            >已上传 {{ videoList.length }}/{{
              getMaxMediaCount()
            }}
            个视频文件</text
          >
        </view>
      </view>

      <PrimaryButton
        text="开始合成"
        @click="startSynthesis"
        style="margin: 50rpx 20rpx"
      />
    </scroll-view>
  </view>
</template>

<script setup>
import { ref , computed  } from "vue";

import PrimaryButton from "@/components/platform/primary-button.vue";
import modal from "@/plugins/modal";
import TemplateSelector from "@/components/platform/template-selector.vue";

import { synthesizeVideo } from "@/api/platform/template";
import Template from "./template.vue";

const template = ref(null);
const videoList = ref([]);

// 计算是否禁用上传组件
const isUploadDisabled = computed(() => {
  return !template.value;
});

/**处理文件上传后的回调 */
const afterRead = (event) => {
  const { file } = event;
  const fileList = Array.isArray(file) ? file : [file];

  fileList.forEach((item) => {
    videoList.value.push({
      ...item,
    });
  });
};


/**删除已上传的视频 */
const deletePic = async (file) => {
  console.log("file:", file);
  console.log("videoList:", videoList.value);
  try {
    const confirm = await modal.confirm("确定要删除这个视频吗？");
    console.log("confirm:", confirm);
    if (!confirm) return;
    videoList.value = videoList.value.filter(
      (item) => item.url != file.file.url
    );
    modal.msg("删除成功");
  } catch (e) {
    modal.msg("删除失败");
  }
};

/**跳转 */
const goBack = () => {
  uni.navigateBack();
  uni.showTabBar({
    animation: true,
  });
};

/**校验表单 */
const validateForm = () => {
  if (!template.value) {
    modal.msg("请选择模板");
    return false;
  }
  if (videoList.value.length == 0) {
    modal.msg("请选择视频");
    return false;
  }
  return true;
};

// 计算最大允许上传的媒体文件数量
const getMaxMediaCount = () => {
  if (!template.value || !template.value.ClipsParam) {
    return 1; // 默认最大数量
  }

  try {
    const clipsParam = JSON.parse(template.value.ClipsParam);
    console.log(
      "长度是什么>>",
      Object.values(clipsParam).filter((value) => value === "mediaId").length
    );
    return (
      Object.values(clipsParam).filter((value) => value === "mediaId").length ||
      1
    );
  } catch (e) {
    console.error("解析 ClipsParam 失败", e);
    return 1;
  }
};

// 获取上传按钮的显示文本
const getUploadText = () => {
  if (!template.value) {
    return "请先选择模板";
  }

  const maxCount = getMaxMediaCount();
  if (videoList.value.length >= maxCount) {
    return `已达到最大上传数量(${maxCount})`;
  }

  return "点击上传视频";
};

/**开始合成视频 */
const startSynthesis = async () => {
  if (!validateForm()) return;
  modal.loading("提交中...");
  try {
    const files = [];
    console.log("列表的数据是什么名", videoList.value);
    videoList.value.forEach((item) => {
      files.push({
        name: item.name,
        uri: item.url,
      });
    });
    console.log("files", files);
    console.log("template.value", template.value);
    const params = {
      templateId: template.value.TemplateId,
      clipsParam: template.value.ClipsParam,
      files: files,
    };
    console.log("params", params);
    const res = await synthesizeVideo(params);
    console.log("res", res);
    modal.closeLoading();
    modal.msg("合成成功");
  } catch (e) {
    modal.closeLoading();
    modal.msg("合成失败:" ,e);
  }
};
</script>

<style lang="scss" scoped>
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;

  /*  #ifdef  MP-WEIXIN  */
  background-color: #070d14;
  /*  #endif  */

  /*  #ifndef  MP-WEIXIN  */
  background-color: #f5f5f5;
  /*  #endif  */

  .container {
    flex: 1;
    margin-top: 175rpx;

    .section {
      margin-bottom: 20rpx;

      .section-title {
        font-size: 32rpx;
        font-weight: bold;
        padding: 20rpx 30rpx 10rpx;

        /*  #ifdef  MP-WEIXIN  */
        color: #e3e2e7;
        /*  #endif  */
        /*  #ifndef  MP-WEIXIN  */
        color: #333;
        /*  #endif  */
      }
    }

    .upload-section {
      margin: 0 20rpx;

      .upload-container {
        /*  #ifdef  MP-WEIXIN  */
        background-color: #1a191e;
        /*  #endif  */
        /*  #ifndef  MP-WEIXIN  */
        background-color: #fff;
        /*  #endif  */
        border-radius: 12rpx;
        padding: 30rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
        border: 1rpx dashed #ccc;
        display: flex;
        flex-direction: column;
        align-items: center;

        :deep(.u-upload) {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          width: 100%;

          .u-upload__wrap {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            width: 100%;
          }

          .u-upload__button {
            /*  #ifdef  MP-WEIXIN  */
            background-color: #2c2b30;
            /*  #endif  */
            /*  #ifndef  MP-WEIXIN  */
            background-color: #f0f0f0;
            /*  #endif  */
            border-radius: 12rpx;
            border: 1rpx dashed #999;
            width: 100rpx !important;
            height: 100rpx !important;
            margin: 10rpx;

            &:active {
              opacity: 0.8;
            }
          }

          .u-upload__preview {
            margin: 10rpx;
            width: 100rpx !important;
            height: 100rpx !important;
            flex: 0 0 auto;

            /* Ensure 3 items per row with proper spacing */
            &:nth-child(3n + 1) {
              margin-left: 0;
            }

            &:nth-child(3n) {
              margin-right: 0;
            }

            &__image {
              border-radius: 8rpx;
              border: 1rpx solid #eee;
              width: 100rpx !important;
              height: 100rpx !important;
            }

            &__del {
              top: 0;
              right: 0;
              background-color: rgba(0, 0, 0, 0.5);
              border-radius: 0 8rpx 0 8rpx;
              width: 30rpx;
              height: 30rpx;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }

        .custom-upload-btn {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;

          .upload-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            margin-bottom: 8rpx;
            /*  #ifdef  MP-WEIXIN  */
            background-color: rgba(64, 128, 255, 0.15);
            color: #4080ff;
            /*  #endif  */
            /*  #ifndef  MP-WEIXIN  */
            background-color: rgba(41, 121, 255, 0.15);
            color: #2979ff;
            /*  #endif  */
            box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

            .iconfont {
              font-family: uniicons;
              font-size: 32rpx;
              line-height: 1;
              font-weight: normal;
              font-style: normal;
              display: inline-flex;
              align-items: center;
              justify-content: center;

              &.icon-video:before {
                content: "\e7c0"; /* Using a better video/film icon */
              }
            }
          }

          .upload-text {
            font-size: 24rpx;
            font-weight: 500;
            /*  #ifdef  MP-WEIXIN  */
            color: #e3e2e7;
            /*  #endif  */
            /*  #ifndef  MP-WEIXIN  */
            color: #666;
            /*  #endif  */
          }
        }
      }

      .upload-tips {
        margin-top: 20rpx;
        padding: 0 30rpx;
        font-size: 24rpx;
        /*  #ifdef  MP-WEIXIN  */
        color: #999;
        /*  #endif  */
        /*  #ifndef  MP-WEIXIN  */
        color: #666;
        /*  #endif  */
      }
    }

    .avatar-section {
      /*  #ifdef  MP-WEIXIN  */
      background-color: #1a191e;
      /*  #endif  */
      /*  #ifndef  MP-WEIXIN  */
      background-color: #fff;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      /*  #endif  */
      margin: 0 20rpx;
      border-radius: 12rpx;
      padding: 20rpx;

      .section-title {
        font-size: 30rpx;
        font-weight: bold;
        /*  #ifdef  MP-WEIXIN  */
        color: #e3e2e7;
        /*  #endif  */
        /*  #ifndef  MP-WEIXIN  */
        color: #333;
        /*  #endif  */
        padding: 0 0 20rpx 0;
      }
    }

    .parameter-picker {
      display: flex;
      justify-content: space-between;
      align-items: center;
      /*  #ifdef  MP-WEIXIN  */
      background-color: #1a191e;
      /*  #endif  */
      /*  #ifndef  MP-WEIXIN  */
      background-color: #fff;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      /*  #endif  */
      height: 100rpx;
      margin: 0 20rpx;
      border-radius: 12rpx;
      padding: 0 20rpx;

      .picker-title {
        font-size: 30rpx;
        /*  #ifdef  MP-WEIXIN  */
        color: #e3e2e7;
        /*  #endif  */
        /*  #ifndef  MP-WEIXIN  */
        color: #333;
        /*  #endif  */
      }

      .picker-value {
        font-size: 30rpx;
        /*  #ifdef  MP-WEIXIN  */
        color: #e3e2e7;
        /*  #endif  */
        /*  #ifndef  MP-WEIXIN  */
        color: #666;
        /*  #endif  */
      }

      .uni-input {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

</style>

